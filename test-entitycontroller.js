/**
 * Test Script for EntityController Hook
 * Run this after loading the main entitycontroller-hook.js
 */

console.log("🧪 Starting EntityController Hook Tests...");

// Wait for the main hook to initialize
setTimeout(() => {
    runTests();
}, 3000);

function runTests() {
    console.log("🚀 Running EntityController Tests...");
    
    // Test 1: Check if functions are available
    console.log("\n📋 Test 1: Function Availability");
    const requiredFunctions = [
        'getAllEntityInstances',
        'autoUpgradeSelected', 
        'autoUpgradeAll',
        'getEntityInfo',
        'selectEntity',
        'monitorEntities'
    ];
    
    requiredFunctions.forEach(funcName => {
        if (typeof global[funcName] === 'function') {
            console.log(`✅ ${funcName} is available`);
        } else {
            console.log(`❌ ${funcName} is NOT available`);
        }
    });
    
    // Test 2: Instance Discovery
    console.log("\n🔍 Test 2: Instance Discovery");
    try {
        const instances = getAllEntityInstances();
        console.log(`✅ Found ${instances.length} EntityController instances`);
        
        if (instances.length > 0) {
            console.log("✅ Instance discovery working");
            
            // Test first few instances
            const testCount = Math.min(3, instances.length);
            console.log(`🔍 Testing first ${testCount} instances:`);
            
            for (let i = 0; i < testCount; i++) {
                try {
                    const info = getEntityInfo(instances[i]);
                    if (info) {
                        console.log(`  Instance ${i}: ID=${info.uniqueId}, Level=${info.currentLevel}/${info.maxLevel}, Selected=${info.isSelected}, CanUpgrade=${info.canUpgrade}`);
                    }
                } catch (error) {
                    console.log(`  ❌ Error testing instance ${i}: ${error}`);
                }
            }
        } else {
            console.log("⚠️ No instances found - game might not be fully loaded");
        }
    } catch (error) {
        console.log(`❌ Instance discovery failed: ${error}`);
    }
    
    // Test 3: Method Calls
    console.log("\n🔧 Test 3: Method Calls");
    try {
        const instances = getAllEntityInstances();
        if (instances.length > 0) {
            const testInstance = instances[0];
            
            // Test basic method calls
            console.log("Testing basic method calls on first instance:");
            
            try {
                const isSelected = testInstance.IsSelected();
                console.log(`✅ IsSelected(): ${isSelected}`);
            } catch (error) {
                console.log(`❌ IsSelected() failed: ${error}`);
            }
            
            try {
                const canUpgrade = testInstance.CanUpgrade(false);
                console.log(`✅ CanUpgrade(): ${canUpgrade}`);
            } catch (error) {
                console.log(`❌ CanUpgrade() failed: ${error}`);
            }
            
            try {
                const level = testInstance.GetLevel();
                console.log(`✅ GetLevel(): ${level}`);
            } catch (error) {
                console.log(`❌ GetLevel() failed: ${error}`);
            }
            
            try {
                const maxLevel = testInstance.GetMaxUpgradeLevel();
                console.log(`✅ GetMaxUpgradeLevel(): ${maxLevel}`);
            } catch (error) {
                console.log(`❌ GetMaxUpgradeLevel() failed: ${error}`);
            }
            
            try {
                const uniqueId = testInstance.get_uniqueId();
                console.log(`✅ get_uniqueId(): ${uniqueId}`);
            } catch (error) {
                console.log(`❌ get_uniqueId() failed: ${error}`);
            }
        }
    } catch (error) {
        console.log(`❌ Method call tests failed: ${error}`);
    }
    
    // Test 4: Statistics
    console.log("\n📊 Test 4: Entity Statistics");
    try {
        const instances = getAllEntityInstances();
        
        if (instances.length > 0) {
            let selectedCount = 0;
            let upgradeableCount = 0;
            let maxLevelCount = 0;
            let levelSum = 0;
            let validInstances = 0;
            
            instances.forEach(instance => {
                try {
                    if (instance.IsSelected()) selectedCount++;
                    if (instance.CanUpgrade(false)) upgradeableCount++;
                    
                    const level = instance.GetLevel();
                    const maxLevel = instance.GetMaxUpgradeLevel();
                    
                    if (level >= maxLevel) maxLevelCount++;
                    levelSum += level;
                    validInstances++;
                } catch (error) {
                    // Skip invalid instances
                }
            });
            
            console.log(`📊 Entity Statistics:`);
            console.log(`   Total instances: ${instances.length}`);
            console.log(`   Valid instances: ${validInstances}`);
            console.log(`   Selected: ${selectedCount}`);
            console.log(`   Can upgrade: ${upgradeableCount}`);
            console.log(`   At max level: ${maxLevelCount}`);
            console.log(`   Average level: ${validInstances > 0 ? (levelSum / validInstances).toFixed(2) : 'N/A'}`);
        }
    } catch (error) {
        console.log(`❌ Statistics test failed: ${error}`);
    }
    
    // Test 5: Monitor Test (short duration)
    console.log("\n👁️ Test 5: Short Monitor Test");
    try {
        console.log("Starting 5-second monitor test...");
        monitorEntities(5000);
    } catch (error) {
        console.log(`❌ Monitor test failed: ${error}`);
    }
    
    console.log("\n✅ EntityController Hook Tests Complete!");
    console.log("📋 If all tests passed, the hook is working correctly.");
    console.log("🎯 You can now use the automation functions safely.");
}

// Helper function to run a safe upgrade test
this.testUpgrade = function() {
    console.log("🧪 Running Safe Upgrade Test...");
    
    try {
        const instances = getAllEntityInstances();
        const upgradeableInstances = instances.filter(i => {
            try {
                return i.CanUpgrade(false) && i.GetLevel() < i.GetMaxUpgradeLevel();
            } catch {
                return false;
            }
        });
        
        if (upgradeableInstances.length === 0) {
            console.log("⚠️ No upgradeable instances found for test");
            return;
        }
        
        // Test with just the first upgradeable instance
        const testInstance = upgradeableInstances[0];
        const beforeLevel = testInstance.GetLevel();
        const maxLevel = testInstance.GetMaxUpgradeLevel();
        const uniqueId = testInstance.get_uniqueId();
        
        console.log(`🎯 Testing upgrade on entity ${uniqueId}: ${beforeLevel}/${maxLevel}`);
        
        if (beforeLevel < maxLevel) {
            testInstance.InstantUpgrade();
            const afterLevel = testInstance.GetLevel();
            
            if (afterLevel > beforeLevel) {
                console.log(`✅ Upgrade test successful: ${beforeLevel} → ${afterLevel}`);
            } else {
                console.log(`⚠️ Upgrade didn't change level: ${beforeLevel} → ${afterLevel}`);
            }
        } else {
            console.log("⚠️ Test instance is already at max level");
        }
        
    } catch (error) {
        console.log(`❌ Upgrade test failed: ${error}`);
    }
};

console.log("🧪 Test script loaded. Tests will run automatically in 3 seconds.");
console.log("💡 You can also run testUpgrade() manually for a safe upgrade test.");
