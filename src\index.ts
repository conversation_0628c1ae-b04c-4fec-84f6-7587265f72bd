import "frida-il2cpp-bridge";

Il2Cpp.perform(function(){
	const AssemblyCSharp = Il2Cpp.domain.assembly("Assembly-CSharp").image;

	// Get the EntityController class
	const EntityController = AssemblyCSharp.class("EntityController");

	console.log("[+] EntityController Auto-Complete Script Loaded");
	console.log(`[+] EntityController class found: ${EntityController.handle}`);

	// Track active EntityController instances
	const activeGoodyHuts = new Set<Il2Cpp.Object>();

	// Helper function to safely call methods with error handling
	function safeMethodCall(instance: Il2Cpp.Object, methodName: string, ...args: any[]): any {
		try {
			const method = instance.method(methodName);
			if (method) {
				return method.invoke(...args);
			} else {
				console.log(`[-] Method ${methodName} not found on instance`);
				return null;
			}
		} catch (error) {
			console.log(`[-] Error calling ${methodName}: ${error}`);
			return null;
		}
	}

	// Function to check if instance can use buy-through
	function canUseBuyThrough(instance: Il2Cpp.Object): boolean {
		try {
			const canBuyThrough = safeMethodCall(instance, "CanBuyThrough");
			return canBuyThrough !== null && canBuyThrough;
		} catch (error) {
			console.log(`[-] Error checking CanBuyThrough: ${error}`);
			return false;
		}
	}

	// Function to check if goody hut is currently collecting
	function isCurrentlyCollecting(instance: Il2Cpp.Object): boolean {
		try {
			// Check if job is not complete and we can't collect (indicates collection in progress)
			const isJobComplete = safeMethodCall(instance, "IsJobComplete");
			const canCollect = safeMethodCall(instance, "CanCollect");

			// If job is not complete and we can't collect, it's likely collecting
			return isJobComplete === false && canCollect === false;
		} catch (error) {
			console.log(`[-] Error checking collection state: ${error}`);
			return false;
		}
	}

	// Function to automatically complete collection
	function autoCompleteCollection(instance: Il2Cpp.Object): boolean {
		try {
			console.log("[*] Attempting auto-completion of goody hut collection...");

			// Check if we can use buy-through
			if (!canUseBuyThrough(instance)) {
				console.log("[-] Cannot use buy-through on this instance");
				return false;
			}

			// Call DoJobBuyThrough to instantly complete
			const result = safeMethodCall(instance, "DoJobBuyThrough");

			if (result !== null) {
				console.log("[+] Successfully auto-completed goody hut collection!");
				return true;
			} else {
				console.log("[-] DoJobBuyThrough call failed");
				return false;
			}
		} catch (error) {
			console.log(`[-] Error in auto-completion: ${error}`);
			return false;
		}
	}

	// Function to scan for collecting goody huts and auto-complete them
	function scanAndAutoComplete(): void {
		try {
			// Use Il2Cpp.gc.choose to find all EntityController instances
			const instances = Il2Cpp.gc.choose(EntityController);

			console.log(`[*] Found ${instances.length} EntityController instances`);

			instances.forEach((instance, index) => {
				try {
					// Check if instance is currently collecting
					if (isCurrentlyCollecting(instance)) {
						console.log(`[*] Instance ${index} appears to be collecting, attempting auto-complete...`);

						if (autoCompleteCollection(instance)) {
							activeGoodyHuts.add(instance);
						}
					}
				} catch (error) {
					console.log(`[-] Error processing instance ${index}: ${error}`);
				}
			});
		} catch (error) {
			console.log(`[-] Error in scanAndAutoComplete: ${error}`);
		}
	}

	// Hook the StartCollect method to automatically trigger completion
	try {
		const startCollectMethod = EntityController.method("StartCollect");

		if (startCollectMethod) {
			console.log("[+] Hooking StartCollect method...");

			startCollectMethod.implementation = function() {
				console.log("[*] StartCollect called - triggering original method first");

				// Store reference to this instance
				const thisInstance = this as Il2Cpp.Object;

				// Call the original method
				const result = thisInstance.method("StartCollect").invoke();

				// Schedule auto-completion after a short delay to let the collection start
				setTimeout(() => {
					try {
						console.log("[*] Attempting auto-completion after StartCollect...");
						if (autoCompleteCollection(thisInstance)) {
							console.log("[+] Auto-completed collection immediately after start!");
						}
					} catch (error) {
						console.log(`[-] Error in delayed auto-completion: ${error}`);
					}
				}, 100); // 100ms delay

				return result;
			};

			console.log("[+] StartCollect hook installed successfully!");
		} else {
			console.log("[-] StartCollect method not found, falling back to periodic scanning");
		}
	} catch (error) {
		console.log(`[-] Error hooking StartCollect: ${error}`);
		console.log("[*] Falling back to periodic scanning method");
	}

	// Periodic scanning as backup method
	console.log("[+] Starting periodic scan for collecting goody huts...");
	setInterval(() => {
		scanAndAutoComplete();
	}, 2000); // Scan every 2 seconds

	// Initial scan
	setTimeout(() => {
		console.log("[*] Performing initial scan...");
		scanAndAutoComplete();
	}, 1000);

	console.log("[+] EntityController auto-completion script fully initialized!");
});
