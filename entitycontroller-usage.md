# EntityController <PERSON><PERSON> Hook Usage Guide

## Overview
This Frida JavaScript script hooks into Unity IL2CPP game's EntityController class to provide automation capabilities for entity management and upgrades.

## Target Information
- **Library**: `libil2cpp.so`
- **Class**: `EntityController` (empty namespace)
- **Framework**: Pure Frida JavaScript (no external dependencies)

## Installation & Usage

### 1. Basic Setup
```bash
# Attach to running game process
frida -U -f com.your.game.package -l entitycontroller-hook.js

# Or attach to already running process
frida -U "Game Name" -l entitycontroller-hook.js
```

### 2. Available Functions

#### Core Functions
```javascript
// Get all EntityController instances
const instances = getAllEntityInstances();
console.log(`Found ${instances.length} entities`);

// Auto-upgrade only selected entities
const upgradedCount = autoUpgradeSelected();
console.log(`Upgraded ${upgradedCount} levels`);

// Auto-upgrade ALL entities (use with caution!)
const totalUpgraded = autoUpgradeAll();
console.log(`Upgraded ${totalUpgraded} total levels`);
```

#### Utility Functions
```javascript
// Get detailed info about an entity
const info = getEntityInfo(instances[0]);
// Returns: {instance, isSelected, canUpgrade, currentLevel, maxLevel, uniqueId}

// Select a specific entity
selectEntity(instances[0]);

// Monitor entities for changes
const monitor = monitorEntities(30000); // Monitor for 30 seconds
```

### 3. Interactive Usage Examples

#### Example 1: Upgrade Selected Buildings
```javascript
// 1. First, get all instances
const entities = getAllEntityInstances();

// 2. Find entities that can be upgraded
const upgradeable = entities.filter(e => e.CanUpgrade(false));
console.log(`Found ${upgradeable.length} upgradeable entities`);

// 3. Select and upgrade specific entities
upgradeable.slice(0, 5).forEach(entity => {
    selectEntity(entity);
    const info = getEntityInfo(entity);
    console.log(`Upgrading entity ${info.uniqueId} from level ${info.currentLevel}`);
});

// 4. Upgrade all selected entities
autoUpgradeSelected();
```

#### Example 2: Batch Processing
```javascript
// Get all entities and process them
const entities = getAllEntityInstances();

entities.forEach((entity, index) => {
    try {
        const info = getEntityInfo(entity);
        
        if (info.canUpgrade && info.currentLevel < info.maxLevel) {
            console.log(`Entity ${index}: ID=${info.uniqueId}, Level=${info.currentLevel}/${info.maxLevel}`);
            
            // Upgrade this entity
            entity.InstantUpgrade();
            console.log(`✅ Upgraded entity ${info.uniqueId}`);
        }
    } catch (error) {
        console.log(`❌ Error processing entity ${index}: ${error}`);
    }
});
```

#### Example 3: Selective Upgrading
```javascript
// Upgrade only entities below a certain level
const entities = getAllEntityInstances();
const targetLevel = 5;

entities.forEach(entity => {
    try {
        const currentLevel = entity.GetLevel();
        const maxLevel = entity.GetMaxUpgradeLevel();
        
        if (currentLevel < targetLevel && currentLevel < maxLevel && entity.CanUpgrade(false)) {
            while (entity.GetLevel() < targetLevel && entity.CanUpgrade(false)) {
                entity.InstantUpgrade();
                console.log(`⚡ Entity upgraded to level ${entity.GetLevel()}`);
            }
        }
    } catch (error) {
        console.log(`❌ Error: ${error}`);
    }
});
```

## Method Reference

### EntityController Methods (Hooked)
- `IsSelected()` - Returns true if entity is currently selected
- `CanUpgrade(bool useAlternateResource)` - Returns true if entity can be upgraded
- `GetLevel()` - Returns current level of the entity
- `GetMaxLevel()` - Returns maximum possible level
- `GetMaxUpgradeLevel()` - Returns maximum upgrade level
- `InstantUpgrade()` - Instantly upgrades the entity by one level
- `Select(bool reselect)` - Selects the entity
- `get_uniqueId()` - Returns unique identifier for the entity

### Global Functions (Added by Script)
- `getAllEntityInstances()` - Returns array of all EntityController instances
- `autoUpgradeSelected()` - Upgrades all selected entities to max level
- `autoUpgradeAll()` - Upgrades ALL entities to max level (dangerous!)
- `getEntityInfo(instance)` - Returns detailed info object for an entity
- `selectEntity(instance)` - Selects a specific entity
- `monitorEntities(duration)` - Monitors entity changes for specified duration

## Safety Notes

⚠️ **Important Warnings:**
1. `autoUpgradeAll()` will upgrade EVERY entity in the game - use with extreme caution
2. Always test with a small number of entities first
3. The script includes safety checks to prevent infinite loops
4. Monitor game stability when using intensive automation

## Troubleshooting

### Common Issues
1. **"EntityController class not found"**
   - Ensure the game is fully loaded
   - Check if the class name is correct for your game version

2. **"No instances found"**
   - Wait for the game to fully initialize
   - Try calling `getAllEntityInstances()` after entering gameplay

3. **"Method not found"**
   - Some methods might have different names in different game versions
   - Check the actual method names using IL2CPP inspection tools

### Debug Commands
```javascript
// Check if hook is working
console.log("Hook status:", isHooked);

// Test instance discovery
const test = getAllEntityInstances();
console.log(`Test found ${test.length} instances`);

// Monitor for a short time
monitorEntities(5000);
```

## Advanced Usage

### Custom Automation
You can extend the script by adding your own functions:

```javascript
// Add this after the script loads
global.customUpgrade = function(maxLevel = 10) {
    const entities = getAllEntityInstances();
    let count = 0;
    
    entities.forEach(entity => {
        try {
            while (entity.GetLevel() < maxLevel && entity.CanUpgrade(false)) {
                entity.InstantUpgrade();
                count++;
            }
        } catch (error) {
            console.log(`Error: ${error}`);
        }
    });
    
    console.log(`Custom upgrade complete: ${count} levels upgraded`);
    return count;
};
```

This hook provides a solid foundation for EntityController automation in Unity IL2CPP games using pure Frida JavaScript.
