/**
 * Simple Frida Test Script for DomiNations
 * This script tests basic IL2CPP connectivity and EntityController discovery
 */

console.log("🚀 Simple Frida Test Starting...");

// Test IL2CPP availability with robust detection
function testIL2CPPAvailability() {
    let attempts = 0;
    const maxAttempts = 15; // 15 attempts = ~30 seconds
    const retryInterval = 2000; // 2 seconds

    function attemptTest() {
        attempts++;
        console.log(`🔍 Testing IL2CPP availability (attempt ${attempts}/${maxAttempts})...`);

        try {
            // First check: Is Il2Cpp object available?
            if (typeof Il2Cpp === 'undefined') {
                console.log(`⏳ IL2CPP API not yet available (attempt ${attempts}/${maxAttempts})`);

                if (attempts < maxAttempts) {
                    setTimeout(attemptTest, retryInterval);
                    return;
                } else {
                    console.log("❌ IL2CPP API never became available - test failed");
                    return;
                }
            }

            console.log("✅ IL2CPP API is available");

            // Second check: Can we access Il2Cpp.domain?
            if (!Il2Cpp.domain) {
                console.log(`⏳ IL2CPP domain not ready yet (attempt ${attempts}/${maxAttempts})`);

                if (attempts < maxAttempts) {
                    setTimeout(attemptTest, retryInterval);
                    return;
                } else {
                    console.log("❌ IL2CPP domain never became ready - test failed");
                    return;
                }
            }

            console.log("✅ IL2CPP domain accessible");

            // Third check: Use Il2Cpp.perform() safely
            Il2Cpp.perform(() => {
            console.log("✅ IL2CPP domain is available");
            
            // Test Assembly-CSharp access
            try {
                const assemblyImage = Il2Cpp.domain.assembly("Assembly-CSharp").image;
                if (assemblyImage) {
                    console.log("✅ Assembly-CSharp found");
                    
                    // Test EntityController class discovery
                    try {
                        const EntityControllerClass = assemblyImage.class("EntityController");
                        if (EntityControllerClass) {
                            console.log("✅ EntityController class found");
                            console.log(`📋 Class name: ${EntityControllerClass.name}`);
                            
                            // Test instance discovery
                            try {
                                const instances = Il2Cpp.gc.choose(EntityControllerClass);
                                console.log(`🔍 Found ${instances.length} EntityController instances`);
                                
                                if (instances.length > 0) {
                                    console.log("✅ Instance discovery successful");
                                    
                                    // Test method access on first instance
                                    try {
                                        const firstInstance = instances[0];
                                        console.log(`📋 Testing methods on first instance...`);
                                        
                                        // Test IsSelected method
                                        try {
                                            const isSelected = firstInstance.IsSelected();
                                            console.log(`✅ IsSelected(): ${isSelected}`);
                                        } catch (error) {
                                            console.log(`❌ IsSelected() failed: ${error}`);
                                        }
                                        
                                        // Test GetLevel method
                                        try {
                                            const level = firstInstance.GetLevel();
                                            console.log(`✅ GetLevel(): ${level}`);
                                        } catch (error) {
                                            console.log(`❌ GetLevel() failed: ${error}`);
                                        }
                                        
                                        // Test CanUpgrade method
                                        try {
                                            const canUpgrade = firstInstance.CanUpgrade(false);
                                            console.log(`✅ CanUpgrade(): ${canUpgrade}`);
                                        } catch (error) {
                                            console.log(`❌ CanUpgrade() failed: ${error}`);
                                        }
                                        
                                    } catch (error) {
                                        console.log(`❌ Method testing failed: ${error}`);
                                    }
                                } else {
                                    console.log("⚠️ No EntityController instances found - game may not be fully loaded");
                                }
                            } catch (error) {
                                console.log(`❌ Instance discovery failed: ${error}`);
                            }
                        } else {
                            console.log("❌ EntityController class not found");
                        }
                    } catch (error) {
                        console.log(`❌ EntityController class discovery failed: ${error}`);
                    }
                } else {
                    console.log("❌ Assembly-CSharp not found");
                }
            } catch (error) {
                console.log(`❌ Assembly-CSharp access failed: ${error}`);
            }
        });
    } catch (error) {
        console.log(`❌ IL2CPP not available: ${error}`);
    }
}, 2000);

console.log("📋 Simple test script loaded. Results will appear in 2 seconds...");
