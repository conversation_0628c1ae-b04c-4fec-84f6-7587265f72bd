/**
 * Frida TypeScript Hook for Unity IL2CPP EntityController Class
 * Target: libil2cpp.so
 * Class: EntityController (empty namespace)
 * Assembly: Assembly-CSharp.dll
 */

import "frida-il2cpp-bridge";

interface EntityControllerMethods {
    IsSelected: Il2Cpp.Method | null;
    CanUpgrade: Il2Cpp.Method | null;
    GetLevel: Il2Cpp.Method | null;
    GetMaxLevel: Il2Cpp.Method | null;
    GetMaxUpgradeLevel: Il2Cpp.Method | null;
    InstantUpgrade: Il2Cpp.Method | null;
    Select: Il2Cpp.Method | null;
    Unselect: Il2Cpp.Method | null;
    GetUniqueId: Il2Cpp.Method | null;
}

interface AutomationStats {
    totalInstances: number;
    selectedInstances: number;
    upgradeableInstances: number;
    upgradesPerformed: number;
    startTime: number;
}

class EntityControllerHook {
    private assemblyImage: Il2Cpp.Image | null = null;
    private entityControllerClass: Il2Cpp.Class | null = null;
    private methods: EntityControllerMethods = {
        IsSelected: null,
        CanUpgrade: null,
        GetLevel: null,
        GetMaxLevel: null,
        GetMaxUpgradeLevel: null,
        InstantUpgrade: null,
        Select: null,
        Unselect: null,
        GetUniqueId: null
    };
    private isHooked: boolean = false;
    private stats: AutomationStats = {
        totalInstances: 0,
        selectedInstances: 0,
        upgradeableInstances: 0,
        upgradesPerformed: 0,
        startTime: Date.now()
    };

    constructor() {
        console.log("🚀 Starting EntityController IL2CPP Hook (TypeScript)...");
    }

    /**
     * Initialize IL2CPP and find EntityController class
     */
    public async initialize(): Promise<boolean> {
        try {
            console.log("🔍 Initializing IL2CPP domain...");

            // Get Assembly-CSharp
            this.assemblyImage = Il2Cpp.domain.assembly("Assembly-CSharp").image;
            if (!this.assemblyImage) {
                console.log("❌ Failed to get Assembly-CSharp image");
                this.listAvailableAssemblies();
                return false;
            }

            console.log("✅ Assembly-CSharp found");

            // Find EntityController class
            this.entityControllerClass = this.assemblyImage.class("EntityController");
            if (!this.entityControllerClass) {
                console.log("❌ EntityController class not found");
                this.listAvailableClasses();
                return false;
            }

            console.log("✅ EntityController class found");
            console.log(`📋 Class info: ${this.entityControllerClass.name}`);

            // Setup method hooks
            this.setupMethods();

            // Setup automation functions
            this.setupGlobalFunctions();

            this.isHooked = true;
            console.log("🎯 EntityController hook setup complete!");

            return true;

        } catch (error) {
            console.log(`❌ Initialization failed: ${error}`);
            return false;
        }
    }

    /**
     * Setup method references and hooks
     */
    private setupMethods(): void {
        try {
            console.log("🔧 Setting up method references...");

            // Core methods
            this.methods.IsSelected = this.entityControllerClass!.method("IsSelected");
            this.methods.CanUpgrade = this.entityControllerClass!.method("CanUpgrade");
            this.methods.GetLevel = this.entityControllerClass!.method("GetLevel");
            this.methods.GetMaxLevel = this.entityControllerClass!.method("GetMaxLevel");
            this.methods.GetMaxUpgradeLevel = this.entityControllerClass!.method("GetMaxUpgradeLevel");
            this.methods.InstantUpgrade = this.entityControllerClass!.method("InstantUpgrade");

            // Additional methods
            try {
                this.methods.Select = this.entityControllerClass!.method("Select");
                this.methods.Unselect = this.entityControllerClass!.method("Unselect");
                this.methods.GetUniqueId = this.entityControllerClass!.method("get_uniqueId");
            } catch (error) {
                console.log(`⚠️ Some optional methods not found: ${error}`);
            }

            // Verify methods found
            Object.entries(this.methods).forEach(([methodName, method]) => {
                if (method) {
                    console.log(`✅ Found method: ${methodName}`);
                } else {
                    console.log(`⚠️ Method not found: ${methodName}`);
                }
            });

            // Hook InstantUpgrade for monitoring
            this.hookInstantUpgrade();

        } catch (error) {
            console.log(`❌ Method setup failed: ${error}`);
        }
    }

    /**
     * Hook InstantUpgrade method for monitoring
     */
    private hookInstantUpgrade(): void {
        if (!this.methods.InstantUpgrade) {
            console.log("⚠️ InstantUpgrade method not available for hooking");
            return;
        }

        try {
            this.methods.InstantUpgrade.implementation = function(this: Il2Cpp.Object) {
                const entityId = this.method("get_uniqueId").invoke();
                const levelBefore = this.method("GetLevel").invoke();
                console.log(`⚡ InstantUpgrade called on entity ${entityId} (level ${levelBefore})`);

                const result = this.method("InstantUpgrade").invoke();

                const levelAfter = this.method("GetLevel").invoke();
                console.log(`✅ Entity ${entityId} upgraded: ${levelBefore} → ${levelAfter}`);

                return result;
            };
            console.log("✅ InstantUpgrade method hooked");
        } catch (error) {
            console.log(`❌ Failed to hook InstantUpgrade: ${error}`);
        }
    }

    /**
     * Get all EntityController instances
     */
    public getAllInstances(): Il2Cpp.Object[] {
        try {
            if (!this.entityControllerClass) {
                console.log("❌ EntityController class not initialized");
                return [];
            }

            const instances = Il2Cpp.gc.choose(this.entityControllerClass);
            this.stats.totalInstances = instances.length;
            console.log(`🔍 Found ${instances.length} EntityController instances`);
            return instances;
        } catch (error) {
            console.log(`❌ Failed to get instances: ${error}`);
            return [];
        }
    }

    /**
     * Auto-upgrade selected entities
     */
    public autoUpgradeSelected(): number {
        try {
            console.log("🚀 Starting auto-upgrade for selected entities...");

            const instances = this.getAllInstances();
            let upgradedCount = 0;
            let selectedCount = 0;

            instances.forEach((instance) => {
                try {
                    // Check if entity is selected
                    const isSelected = instance.method("IsSelected").invoke();
                    if (isSelected) {
                        selectedCount++;
                        console.log(`🎯 Processing selected entity`);

                        // Check if can upgrade
                        const canUpgrade = instance.method("CanUpgrade").invoke(false);
                        if (canUpgrade) {
                            const currentLevel = instance.method("GetLevel").invoke();
                            const maxLevel = instance.method("GetMaxUpgradeLevel").invoke();

                            console.log(`📊 Entity level: ${currentLevel}/${maxLevel}`);

                            // Upgrade until max level
                            let upgradeAttempts = 0;
                            const maxUpgradeAttempts = 50; // Safety limit

                            while (currentLevel < maxLevel && 
                                   instance.method("CanUpgrade").invoke(false) && 
                                   upgradeAttempts < maxUpgradeAttempts) {
                                
                                const levelBefore = instance.method("GetLevel").invoke();
                                instance.method("InstantUpgrade").invoke();
                                const levelAfter = instance.method("GetLevel").invoke();
                                
                                if (levelAfter > levelBefore) {
                                    upgradedCount++;
                                    console.log(`⚡ Upgraded entity to level ${levelAfter}`);
                                } else {
                                    console.log("⚠️ Level didn't change, stopping upgrade loop");
                                    break;
                                }
                                
                                upgradeAttempts++;
                            }

                            if (upgradeAttempts >= maxUpgradeAttempts) {
                                console.log("⚠️ Reached maximum upgrade attempts, stopping");
                            }
                        } else {
                            console.log("⚠️ Entity cannot be upgraded");
                        }
                    }
                } catch (error) {
                    console.log(`❌ Error processing entity: ${error}`);
                }
            });

            this.stats.selectedInstances = selectedCount;
            this.stats.upgradesPerformed += upgradedCount;

            console.log(`✅ Auto-upgrade complete! Selected: ${selectedCount}, Upgraded: ${upgradedCount} levels`);
            return upgradedCount;

        } catch (error) {
            console.log(`❌ Auto-upgrade failed: ${error}`);
            return 0;
        }
    }

    /**
     * Get entity information
     */
    public getEntityInfo(instance: Il2Cpp.Object): any {
        try {
            if (!instance) {
                console.log("❌ No instance provided");
                return null;
            }

            const info = {
                isSelected: instance.method("IsSelected").invoke(),
                canUpgrade: instance.method("CanUpgrade").invoke(false),
                currentLevel: instance.method("GetLevel").invoke(),
                maxLevel: instance.method("GetMaxUpgradeLevel").invoke(),
                uniqueId: instance.method("get_uniqueId").invoke()
            };

            console.log(`📋 Entity Info: ID=${info.uniqueId}, Selected=${info.isSelected}, Level=${info.currentLevel}/${info.maxLevel}, CanUpgrade=${info.canUpgrade}`);
            return info;

        } catch (error) {
            console.log(`❌ Failed to get entity info: ${error}`);
            return null;
        }
    }

    /**
     * Setup global automation functions
     */
    private setupGlobalFunctions(): void {
        console.log("🤖 Setting up global automation functions...");

        // Make methods available globally
        (global as any).getAllEntityInstances = () => this.getAllInstances();
        (global as any).autoUpgradeSelected = () => this.autoUpgradeSelected();
        (global as any).getEntityInfo = (instance: Il2Cpp.Object) => this.getEntityInfo(instance);
        (global as any).getEntityStats = () => this.getStats();

        console.log("✅ Global functions ready!");
        console.log("📋 Available functions:");
        console.log("   - getAllEntityInstances()");
        console.log("   - autoUpgradeSelected()");
        console.log("   - getEntityInfo(instance)");
        console.log("   - getEntityStats()");
    }

    /**
     * Get current statistics
     */
    public getStats(): AutomationStats {
        const runtime = Date.now() - this.stats.startTime;
        console.log(`📊 Stats: Total: ${this.stats.totalInstances}, Selected: ${this.stats.selectedInstances}, Upgrades: ${this.stats.upgradesPerformed}, Runtime: ${runtime}ms`);
        return { ...this.stats };
    }

    /**
     * List available assemblies for debugging
     */
    private listAvailableAssemblies(): void {
        try {
            console.log("💡 Available assemblies:");
            const assemblies = Il2Cpp.domain.assemblies;
            assemblies.slice(0, 10).forEach((assembly, index) => {
                console.log(`   ${index}: ${assembly.name}`);
            });
        } catch (error) {
            console.log("   Could not enumerate assemblies");
        }
    }

    /**
     * List available classes for debugging
     */
    private listAvailableClasses(): void {
        if (!this.assemblyImage) return;

        try {
            console.log("💡 Available classes (first 10):");
            const classes = this.assemblyImage.classes;
            for (let i = 0; i < Math.min(10, classes.length); i++) {
                console.log(`   ${i}: ${classes[i].name}`);
            }

            // Look for classes containing "Entity" or "Controller"
            const entityClasses = classes.filter(cls =>
                cls.name.toLowerCase().includes('entity') ||
                cls.name.toLowerCase().includes('controller')
            );

            if (entityClasses.length > 0) {
                console.log("🔍 Found entity/controller related classes:");
                entityClasses.forEach((cls, index) => {
                    console.log(`   ${index}: ${cls.name}`);
                });
            }
        } catch (error) {
            console.log("   Could not enumerate classes");
        }
    }
}

// Script execution entry point
Il2Cpp.perform(async () => {
    console.log("🚀 EntityController Hook - Il2Cpp bridge context established");

    try {
        // Wait for game to be fully loaded
        await new Promise(resolve => setTimeout(resolve, 5000));

        const hook = new EntityControllerHook();
        const initialized = await hook.initialize();

        if (initialized) {
            console.log("✅ EntityController hook ready!");
            
            // Make hook instance available globally for debugging
            (global as any).entityHook = hook;
            
            // Optional: Start monitoring
            console.log("💡 Use autoUpgradeSelected() to upgrade selected entities");
        } else {
            console.log("❌ Failed to initialize EntityController hook");
        }

    } catch (error) {
        console.log(`❌ Fatal error: ${error}`);
    }
});
