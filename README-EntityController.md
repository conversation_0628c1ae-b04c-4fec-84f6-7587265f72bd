# EntityController Hook - TypeScript with frida-il2cpp-bridge

A TypeScript-based Frida hook for the Unity IL2CPP EntityController class in Dominations game, using frida-il2cpp-bridge for robust IL2CPP interaction.

## Features

- **TypeScript Implementation**: Type-safe hook development with proper interfaces
- **frida-il2cpp-bridge**: Uses the modern frida-il2cpp-bridge library for reliable IL2CPP hooking
- **Auto-Upgrade Functionality**: Automatically upgrades selected entities to maximum level
- **Method Monitoring**: Hooks InstantUpgrade method to monitor upgrade activities
- **Instance Discovery**: Finds and manages all EntityController instances
- **Error Handling**: Comprehensive error handling and recovery mechanisms
- **Statistics Tracking**: Tracks automation performance and statistics

## Target Information

- **Library**: libil2cpp.so
- **Assembly**: Assembly-CSharp.dll
- **Class**: EntityController (empty namespace)
- **Key Methods**:
  - `IsSelected()` - RVA: 0x1E53050
  - `CanUpgrade(bool useAlternateResource = False)` - RVA: 0x1E4027C
  - `GetLevel()` - RVA: 0x1E365A4
  - `GetMaxUpgradeLevel()` - RVA: 0x1E4A0B8
  - `InstantUpgrade()` - RVA: 0x1E40540

## Installation

1. **Install Dependencies**:
   ```bash
   npm install
   ```

2. **Build the Hook**:
   ```bash
   npm run build-entitycontroller
   ```

3. **Deploy to Device**:
   ```bash
   frida -U -l dist/entitycontroller-hook.js com.nexonm.dominations.adk
   ```

## Usage

### Basic Usage

Once the hook is loaded, the following global functions become available:

```javascript
// Get all EntityController instances
const instances = getAllEntityInstances();

// Auto-upgrade all selected entities
const upgradesPerformed = autoUpgradeSelected();

// Get detailed information about an entity
const info = getEntityInfo(instances[0]);

// Get current statistics
const stats = getEntityStats();
```

### Auto-Upgrade Selected Entities

The main automation function upgrades all selected entities to their maximum level:

```javascript
// This will:
// 1. Find all EntityController instances
// 2. Check which ones are selected (IsSelected() returns true)
// 3. For each selected entity, upgrade until max level
// 4. Return the total number of upgrades performed
const upgrades = autoUpgradeSelected();
console.log(`Performed ${upgrades} upgrades`);
```

### Entity Information

Get detailed information about any EntityController instance:

```javascript
const instances = getAllEntityInstances();
if (instances.length > 0) {
    const info = getEntityInfo(instances[0]);
    // Returns: {
    //   isSelected: boolean,
    //   canUpgrade: boolean,
    //   currentLevel: number,
    //   maxLevel: number,
    //   uniqueId: number
    // }
}
```

### Statistics Tracking

Monitor automation performance:

```javascript
const stats = getEntityStats();
// Returns: {
//   totalInstances: number,
//   selectedInstances: number,
//   upgradeableInstances: number,
//   upgradesPerformed: number,
//   startTime: number
// }
```

## Architecture

### Class Structure

```typescript
class EntityControllerHook {
    private assemblyImage: Il2Cpp.Image | null;
    private entityControllerClass: Il2Cpp.Class | null;
    private methods: EntityControllerMethods;
    private isHooked: boolean;
    private stats: AutomationStats;
}
```

### Method Interface

```typescript
interface EntityControllerMethods {
    IsSelected: Il2Cpp.Method | null;
    CanUpgrade: Il2Cpp.Method | null;
    GetLevel: Il2Cpp.Method | null;
    GetMaxLevel: Il2Cpp.Method | null;
    GetMaxUpgradeLevel: Il2Cpp.Method | null;
    InstantUpgrade: Il2Cpp.Method | null;
    Select: Il2Cpp.Method | null;
    Unselect: Il2Cpp.Method | null;
    GetUniqueId: Il2Cpp.Method | null;
}
```

## Key Features

### 1. Robust Initialization

- Waits for IL2CPP domain to be ready
- Validates Assembly-CSharp availability
- Finds EntityController class with fallback discovery
- Sets up method references with error handling

### 2. Method Hooking

- Hooks `InstantUpgrade` method for monitoring
- Logs upgrade activities with entity ID and level changes
- Non-intrusive monitoring that doesn't affect game performance

### 3. Auto-Upgrade Logic

- Finds all EntityController instances using `Il2Cpp.gc.choose()`
- Filters for selected entities using `IsSelected()`
- Checks upgrade eligibility with `CanUpgrade(false)`
- Performs upgrades until maximum level reached
- Includes safety mechanisms to prevent infinite loops

### 4. Error Handling

- Comprehensive try-catch blocks around all operations
- Graceful degradation when methods are not found
- Detailed error logging for debugging
- Recovery mechanisms for connection issues

## Debugging

### Available Debug Functions

```javascript
// Access the hook instance for debugging
entityHook.getAllInstances();
entityHook.getStats();

// Manual reinitialization if needed
entityHook.initialize();
```

### Common Issues

1. **Class Not Found**: Check if the game is fully loaded
2. **Method Not Found**: Verify the game version matches expected RVAs
3. **No Instances**: Ensure you're in the game world, not menus
4. **Upgrade Failures**: Check if entities have sufficient resources

## Performance

- **Memory Efficient**: Uses weak references where possible
- **Non-Blocking**: Asynchronous initialization
- **Optimized Loops**: Safety limits prevent infinite loops
- **Minimal Overhead**: Only hooks essential methods

## Compatibility

- **Frida Version**: 16.0.0+
- **frida-il2cpp-bridge**: Latest version
- **Target Game**: Dominations (Unity IL2CPP)
- **Platform**: Android/iOS

## Development

### Building from Source

```bash
# Install dependencies
npm install

# Build TypeScript to JavaScript
npm run build-entitycontroller

# Watch for changes during development
npm run watch
```

### Testing

```bash
# Test the compiled hook
frida -U -l dist/entitycontroller-hook.js com.nexonm.dominations.adk

# Check console output for initialization status
# Look for "✅ EntityController hook ready!" message
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make changes to `src/entitycontroller-hook.ts`
4. Test thoroughly with the target game
5. Submit a pull request

## License

This project is for educational purposes only. Use responsibly and in accordance with the game's terms of service.
