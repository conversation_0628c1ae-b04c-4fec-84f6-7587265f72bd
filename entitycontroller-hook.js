/**
 * Frida JavaScript Hook for Unity IL2CPP EntityController Class
 * Target: libil2cpp.so
 * Class: EntityController (empty namespace)
 */

console.log("🚀 Starting EntityController IL2CPP Hook...");

// Global variables
let EntityControllerClass = null;
let assemblyImage = null;
let isHooked = false;

// Method pointers - will be resolved dynamically
let methods = {
    IsSelected: null,
    CanUpgrade: null,
    GetLevel: null,
    GetMaxLevel: null,
    GetMaxUpgradeLevel: null,
    InstantUpgrade: null
};

/**
 * Initialize IL2CPP and find EntityController class with enhanced detection
 */
function initializeIL2CPP() {
    console.log("🔍 Starting enhanced IL2CPP initialization...");

    let attempts = 0;
    const maxAttempts = 20; // 20 attempts with exponential backoff
    let retryInterval = 1000; // Start with 1 second, will increase

    // First, perform comprehensive environment diagnostics
    performEnvironmentDiagnostics();

    function attemptIL2CPPInitialization() {
        attempts++;
        console.log(`� IL2CPP initialization attempt ${attempts}/${maxAttempts} (delay: ${retryInterval}ms)...`);

        try {
            // Enhanced IL2CPP detection strategy
            if (!detectIL2CPPAvailability()) {
                if (attempts < maxAttempts) {
                    console.log(`⏳ Retrying in ${retryInterval/1000}s... (exponential backoff)`);
                    setTimeout(attemptIL2CPPInitialization, retryInterval);
                    retryInterval = Math.min(retryInterval * 1.5, 8000); // Exponential backoff, max 8s
                    return;
                } else {
                    console.log("❌ IL2CPP detection failed after all attempts");
                    console.log("� Trying fallback native module detection...");
                    tryNativeModuleDetection();
                    return;
                }
            }

            console.log("✅ IL2CPP API is available, attempting domain access...");

            // Second check: Can we access Il2Cpp.domain?
            try {
                if (!Il2Cpp.domain) {
                    console.log(`⏳ IL2CPP domain not ready yet (attempt ${attempts}/${maxAttempts})`);

                    if (attempts < maxAttempts) {
                        setTimeout(attemptIL2CPPInitialization, retryInterval);
                        return;
                    } else {
                        console.log("❌ IL2CPP domain never became ready - timeout reached");
                        return;
                    }
                }

                console.log("✅ IL2CPP domain accessible, using Il2Cpp.perform()...");

                // Third check: Use Il2Cpp.perform() safely
                Il2Cpp.perform(() => {
                    try {
                        console.log("✅ Inside Il2Cpp.perform() - IL2CPP fully ready");

                        // Get Assembly-CSharp
                        console.log("🔍 Looking for Assembly-CSharp...");
                        assemblyImage = Il2Cpp.domain.assembly("Assembly-CSharp").image;
                        if (!assemblyImage) {
                            console.log("❌ Failed to get Assembly-CSharp image");
                            console.log("💡 Available assemblies:");
                            try {
                                const assemblies = Il2Cpp.domain.assemblies;
                                assemblies.forEach((assembly, index) => {
                                    console.log(`   ${index}: ${assembly.name}`);
                                });
                            } catch (e) {
                                console.log("   Could not enumerate assemblies");
                            }
                            return;
                        }

                        console.log("✅ Assembly-CSharp found");

                        // Find EntityController class
                        console.log("🔍 Looking for EntityController class...");
                        EntityControllerClass = assemblyImage.class("EntityController");
                        if (!EntityControllerClass) {
                            console.log("❌ EntityController class not found");
                            console.log("💡 Trying alternative class discovery...");

                            // Try to enumerate some classes to see what's available
                            try {
                                const classes = assemblyImage.classes;
                                console.log("📋 Available classes (first 10):");
                                for (let i = 0; i < Math.min(10, classes.length); i++) {
                                    console.log(`   ${i}: ${classes[i].name}`);
                                }

                                // Look for classes containing "Entity" or "Controller"
                                const entityClasses = classes.filter(cls =>
                                    cls.name.toLowerCase().includes('entity') ||
                                    cls.name.toLowerCase().includes('controller')
                                );

                                if (entityClasses.length > 0) {
                                    console.log("🔍 Found entity/controller related classes:");
                                    entityClasses.forEach((cls, index) => {
                                        console.log(`   ${index}: ${cls.name}`);
                                    });
                                }
                            } catch (e) {
                                console.log("   Could not enumerate classes");
                            }
                            return;
                        }

                        console.log("✅ EntityController class found");
                        console.log(`📋 Class info: ${EntityControllerClass.name}`);

                        // Test instance discovery
                        testInstanceDiscovery();

                        // Setup method hooks
                        setupMethodHooks();

                        // Setup automation functions
                        setupAutomationFunctions();

                        isHooked = true;
                        console.log("🎯 EntityController hook setup complete!");

                    } catch (performError) {
                        console.log(`❌ Error inside Il2Cpp.perform(): ${performError}`);
                        console.log(`📋 Error details: ${performError.stack}`);
                    }
                });

            } catch (domainError) {
                console.log(`❌ IL2CPP domain access failed: ${domainError}`);

                if (attempts < maxAttempts) {
                    console.log(`⏳ Retrying in ${retryInterval/1000} seconds...`);
                    setTimeout(attemptIL2CPPInitialization, retryInterval);
                    return;
                } else {
                    console.log("❌ IL2CPP domain access timeout reached");
                }
            }

        } catch (error) {
            console.log(`❌ IL2CPP initialization attempt ${attempts} failed: ${error}`);
            console.log(`📋 Error details: ${error.stack}`);

            if (attempts < maxAttempts) {
                console.log(`⏳ Retrying in ${retryInterval/1000} seconds...`);
                setTimeout(attemptIL2CPPInitialization, retryInterval);
            } else {
                console.log("❌ IL2CPP initialization timeout - all attempts exhausted");
                console.log("💡 Possible causes:");
                console.log("   - Game is not using IL2CPP");
                console.log("   - Game hasn't fully loaded yet");
                console.log("   - Frida version incompatibility");
                console.log("   - Game has anti-debugging measures");
            }
        }
    }

    // Start the initialization attempts
    attemptIL2CPPInitialization();
}

/**
 * Perform comprehensive environment diagnostics
 */
function performEnvironmentDiagnostics() {
    console.log("🔍 Performing environment diagnostics...");

    try {
        // Check Frida version and environment
        console.log(`📋 Frida version: ${Frida.version}`);
        console.log(`📋 Process: ${Process.id} (${Process.arch})`);

        // Check available global objects
        console.log("🌐 Available global objects:");
        const globalProps = Object.getOwnPropertyNames(this).filter(prop =>
            !prop.startsWith('_') && typeof this[prop] === 'object'
        );
        globalProps.slice(0, 10).forEach(prop => {
            console.log(`   - ${prop}: ${typeof this[prop]}`);
        });

        // Check for Unity/IL2CPP modules
        console.log("📚 Checking for Unity/IL2CPP modules...");
        const modules = Process.enumerateModules();
        const unityModules = modules.filter(m =>
            m.name.toLowerCase().includes('il2cpp') ||
            m.name.toLowerCase().includes('unity') ||
            m.name.toLowerCase().includes('libil2cpp')
        );

        if (unityModules.length > 0) {
            console.log("✅ Found Unity/IL2CPP modules:");
            unityModules.forEach(m => {
                console.log(`   - ${m.name} @ ${m.base}`);
            });
        } else {
            console.log("⚠️ No Unity/IL2CPP modules found in loaded modules");
            console.log("📋 All loaded modules:");
            modules.slice(0, 15).forEach(m => {
                console.log(`   - ${m.name}`);
            });
        }

        // Check for libil2cpp.so specifically
        const il2cppBase = Module.findBaseAddress("libil2cpp.so");
        if (il2cppBase) {
            console.log(`✅ libil2cpp.so found at: ${il2cppBase}`);
        } else {
            console.log("❌ libil2cpp.so not found");
        }

    } catch (error) {
        console.log(`❌ Environment diagnostics failed: ${error}`);
    }
}

/**
 * Enhanced IL2CPP availability detection
 */
function detectIL2CPPAvailability() {
    try {
        // Method 1: Check if Il2Cpp object exists
        if (typeof Il2Cpp === 'undefined') {
            console.log("❌ Il2Cpp object is undefined");
            return false;
        }

        console.log("✅ Il2Cpp object exists");

        // Method 2: Check Il2Cpp.api availability
        if (typeof Il2Cpp.api === 'undefined') {
            console.log("❌ Il2Cpp.api is undefined");
            return false;
        }

        console.log("✅ Il2Cpp.api is available");

        // Method 3: Check domain availability
        if (!Il2Cpp.domain) {
            console.log("❌ Il2Cpp.domain is not available");
            return false;
        }

        console.log("✅ Il2Cpp.domain is available");

        // Method 4: Test basic domain access
        try {
            const assemblies = Il2Cpp.domain.assemblies;
            console.log(`✅ Can access domain assemblies (${assemblies.length} found)`);
            return true;
        } catch (domainError) {
            console.log(`❌ Cannot access domain assemblies: ${domainError}`);
            return false;
        }

    } catch (error) {
        console.log(`❌ IL2CPP detection error: ${error}`);
        return false;
    }
}

/**
 * Fallback native module detection and hooking
 */
function tryNativeModuleDetection() {
    console.log("🔄 Attempting native module detection as fallback...");

    try {
        const il2cppBase = Module.findBaseAddress("libil2cpp.so");
        if (!il2cppBase) {
            console.log("❌ libil2cpp.so not found - cannot proceed with native hooking");
            return false;
        }

        console.log(`✅ libil2cpp.so found at: ${il2cppBase}`);

        // Try to find EntityController methods using known RVAs from codebase analysis
        const knownMethods = {
            IsSelected: "0x1E53050",
            CanUpgrade: "0x1E4027C",
            GetLevel: "0x1E365A4",
            GetMaxUpgradeLevel: "0x1E4A0B8",
            InstantUpgrade: "0x1E40540"
        };

        console.log("🔍 Attempting to resolve EntityController methods by RVA...");
        let resolvedMethods = 0;

        Object.keys(knownMethods).forEach(methodName => {
            try {
                const rva = knownMethods[methodName];
                const address = il2cppBase.add(rva);

                // Try to read the address to see if it's valid
                const instruction = Instruction.parse(address);
                if (instruction) {
                    console.log(`✅ ${methodName} found at ${address} (RVA: ${rva})`);
                    resolvedMethods++;
                } else {
                    console.log(`⚠️ ${methodName} at ${address} (RVA: ${rva}) - cannot parse instruction`);
                }
            } catch (error) {
                console.log(`❌ ${methodName} resolution failed: ${error}`);
            }
        });

        if (resolvedMethods > 0) {
            console.log(`✅ Resolved ${resolvedMethods}/${Object.keys(knownMethods).length} methods via native detection`);
            console.log("💡 Native hooking approach is viable - consider implementing direct method hooks");
            return true;
        } else {
            console.log("❌ No methods resolved via native detection");
            return false;
        }

    } catch (error) {
        console.log(`❌ Native module detection failed: ${error}`);
        return false;
    }
}

/**
 * Test if we can discover EntityController instances
 */
function testInstanceDiscovery() {
    try {
        const instances = Il2Cpp.gc.choose(EntityControllerClass);
        console.log(`🔍 Found ${instances.length} EntityController instances`);
        
        if (instances.length > 0) {
            console.log("✅ Instance discovery working");
            
            // Test first instance
            const firstInstance = instances[0];
            console.log(`📋 First instance: ${firstInstance}`);
        }
    } catch (error) {
        console.log(`⚠️ Instance discovery test failed: ${error}`);
    }
}

/**
 * Setup method hooks for EntityController
 */
function setupMethodHooks() {
    try {
        console.log("🔧 Setting up method hooks...");

        // Find methods by name
        methods.IsSelected = EntityControllerClass.method("IsSelected");
        methods.CanUpgrade = EntityControllerClass.method("CanUpgrade");
        methods.GetLevel = EntityControllerClass.method("GetLevel");
        methods.GetMaxLevel = EntityControllerClass.method("GetMaxLevel");
        methods.GetMaxUpgradeLevel = EntityControllerClass.method("GetMaxUpgradeLevel");
        methods.InstantUpgrade = EntityControllerClass.method("InstantUpgrade");

        // Also try to find additional useful methods
        try {
            methods.Select = EntityControllerClass.method("Select");
            methods.Unselect = EntityControllerClass.method("Unselect");
            methods.GetUniqueId = EntityControllerClass.method("get_uniqueId");
            methods.CanCollect = EntityControllerClass.method("CanCollect");
            methods.StartCollect = EntityControllerClass.method("StartCollect");
            methods.FinishCollect = EntityControllerClass.method("FinishCollect");
        } catch (error) {
            console.log(`⚠️ Some optional methods not found: ${error}`);
        }

        // Verify methods found
        Object.keys(methods).forEach(methodName => {
            if (methods[methodName]) {
                console.log(`✅ Found method: ${methodName}`);
            } else {
                console.log(`⚠️ Method not found: ${methodName}`);
            }
        });

        // Hook IsSelected method for monitoring (optional - can be disabled)
        if (methods.IsSelected && false) { // Disabled by default to reduce spam
            methods.IsSelected.implementation = function() {
                const result = this.IsSelected();
                if (result) {
                    console.log(`🎯 Entity ${this.get_uniqueId()} is selected`);
                }
                return result;
            };
            console.log("✅ IsSelected method hooked");
        }

        // Hook InstantUpgrade for monitoring
        if (methods.InstantUpgrade) {
            methods.InstantUpgrade.implementation = function() {
                const entityId = this.get_uniqueId();
                const levelBefore = this.GetLevel();
                console.log(`⚡ InstantUpgrade called on entity ${entityId} (level ${levelBefore})`);

                const result = this.InstantUpgrade();

                const levelAfter = this.GetLevel();
                console.log(`✅ Entity ${entityId} upgraded: ${levelBefore} → ${levelAfter}`);

                return result;
            };
            console.log("✅ InstantUpgrade method hooked");
        }

    } catch (error) {
        console.log(`❌ Method hook setup failed: ${error}`);
    }
}

/**
 * Setup automation functions
 */
function setupAutomationFunctions() {
    console.log("🤖 Setting up automation functions...");
    
    // Global function to get all EntityController instances
    this.getAllEntityInstances = function() {
        try {
            if (!EntityControllerClass) {
                console.log("❌ EntityController class not initialized");
                return [];
            }

            const instances = Il2Cpp.gc.choose(EntityControllerClass);
            console.log(`🔍 Found ${instances.length} EntityController instances`);
            return instances;
        } catch (error) {
            console.log(`❌ Failed to get instances: ${error}`);
            return [];
        }
    };

    // Global function to auto-upgrade selected entities
    this.autoUpgradeSelected = function() {
        try {
            console.log("🚀 Starting auto-upgrade for selected entities...");

            const instances = this.getAllEntityInstances();
            let upgradedCount = 0;
            
            instances.forEach(instance => {
                try {
                    // Check if entity is selected
                    if (instance.IsSelected()) {
                        console.log(`🎯 Processing selected entity: ${instance}`);
                        
                        // Check if can upgrade
                        if (instance.CanUpgrade(false)) {
                            const currentLevel = instance.GetLevel();
                            const maxLevel = instance.GetMaxUpgradeLevel();
                            
                            console.log(`📊 Entity level: ${currentLevel}/${maxLevel}`);
                            
                            // Upgrade until max level
                            while (currentLevel < maxLevel && instance.CanUpgrade(false)) {
                                instance.InstantUpgrade();
                                upgradedCount++;
                                console.log(`⚡ Upgraded entity to level ${instance.GetLevel()}`);
                                
                                // Safety check to prevent infinite loop
                                if (instance.GetLevel() === currentLevel) {
                                    console.log("⚠️ Level didn't change, stopping upgrade loop");
                                    break;
                                }
                            }
                        } else {
                            console.log("⚠️ Entity cannot be upgraded");
                        }
                    }
                } catch (error) {
                    console.log(`❌ Error processing entity: ${error}`);
                }
            });
            
            console.log(`✅ Auto-upgrade complete! Upgraded ${upgradedCount} levels`);
            return upgradedCount;
            
        } catch (error) {
            console.log(`❌ Auto-upgrade failed: ${error}`);
            return 0;
        }
    };
    
    // Global function to upgrade all entities (not just selected)
    this.autoUpgradeAll = function() {
        try {
            console.log("🚀 Starting auto-upgrade for ALL entities...");

            const instances = this.getAllEntityInstances();
            let upgradedCount = 0;
            
            instances.forEach(instance => {
                try {
                    // Check if can upgrade
                    if (instance.CanUpgrade(false)) {
                        const currentLevel = instance.GetLevel();
                        const maxLevel = instance.GetMaxUpgradeLevel();
                        
                        console.log(`📊 Entity level: ${currentLevel}/${maxLevel}`);
                        
                        // Upgrade until max level
                        while (currentLevel < maxLevel && instance.CanUpgrade(false)) {
                            instance.InstantUpgrade();
                            upgradedCount++;
                            
                            // Safety check to prevent infinite loop
                            if (instance.GetLevel() === currentLevel) {
                                console.log("⚠️ Level didn't change, stopping upgrade loop");
                                break;
                            }
                        }
                    }
                } catch (error) {
                    console.log(`❌ Error processing entity: ${error}`);
                }
            });
            
            console.log(`✅ Auto-upgrade ALL complete! Upgraded ${upgradedCount} levels`);
            return upgradedCount;
            
        } catch (error) {
            console.log(`❌ Auto-upgrade ALL failed: ${error}`);
            return 0;
        }
    };
    
    // Global function to get entity info
    this.getEntityInfo = function(instance) {
        try {
            if (!instance) {
                console.log("❌ No instance provided");
                return null;
            }

            const info = {
                instance: instance,
                isSelected: instance.IsSelected(),
                canUpgrade: instance.CanUpgrade(false),
                currentLevel: instance.GetLevel(),
                maxLevel: instance.GetMaxUpgradeLevel(),
                uniqueId: instance.get_uniqueId()
            };

            console.log(`📋 Entity Info: ID=${info.uniqueId}, Selected=${info.isSelected}, Level=${info.currentLevel}/${info.maxLevel}, CanUpgrade=${info.canUpgrade}`);
            return info;

        } catch (error) {
            console.log(`❌ Failed to get entity info: ${error}`);
            return null;
        }
    };

    // Global function to force select an entity
    this.selectEntity = function(instance) {
        try {
            if (!instance) {
                console.log("❌ No instance provided");
                return false;
            }

            const selectMethod = EntityControllerClass.method("Select");
            if (selectMethod) {
                instance.Select(false);
                console.log(`✅ Entity ${instance.get_uniqueId()} selected`);
                return true;
            } else {
                console.log("❌ Select method not found");
                return false;
            }
        } catch (error) {
            console.log(`❌ Failed to select entity: ${error}`);
            return false;
        }
    };

    // Global function to monitor entity changes
    this.monitorEntities = function(duration = 10000) {
        console.log(`🔍 Starting entity monitoring for ${duration}ms...`);

        const startTime = Date.now();
        const monitorInterval = setInterval(() => {
            try {
                const instances = this.getAllEntityInstances();
                const selectedCount = instances.filter(i => i.IsSelected()).length;
                const upgradeableCount = instances.filter(i => i.CanUpgrade(false)).length;

                console.log(`📊 Monitor: ${instances.length} total, ${selectedCount} selected, ${upgradeableCount} upgradeable`);

                if (Date.now() - startTime >= duration) {
                    clearInterval(monitorInterval);
                    console.log("✅ Entity monitoring complete");
                }
            } catch (error) {
                console.log(`❌ Monitor error: ${error}`);
                clearInterval(monitorInterval);
            }
        }, 2000);

        return monitorInterval;
    };

    console.log("✅ Extended automation functions ready!");
    console.log("📋 Available functions:");
    console.log("   - getAllEntityInstances()");
    console.log("   - autoUpgradeSelected()");
    console.log("   - autoUpgradeAll()");
    console.log("   - getEntityInfo(instance)");
    console.log("   - selectEntity(instance)");
    console.log("   - monitorEntities(duration)");
}

/**
 * Utility function to wait for game to be ready
 */
function waitForGameReady(callback, maxAttempts = 30) {
    let attempts = 0;

    const checkReady = () => {
        attempts++;
        console.log(`🔍 Checking game readiness... (${attempts}/${maxAttempts})`);

        try {
            const instances = Il2Cpp.gc.choose(EntityControllerClass);
            if (instances.length > 0) {
                console.log("✅ Game appears ready - found EntityController instances");
                callback();
                return;
            }
        } catch (error) {
            console.log(`⚠️ Game not ready yet: ${error}`);
        }

        if (attempts < maxAttempts) {
            setTimeout(checkReady, 2000);
        } else {
            console.log("❌ Game readiness timeout - proceeding anyway");
            callback();
        }
    };

    checkReady();
}

// Initialize when script loads - give game more time to start up
console.log("📋 EntityController Hook Script Loaded");
console.log("⏳ Waiting 8 seconds for game to fully initialize before starting IL2CPP detection...");

// For Android IL2CPP games, we might need Java.perform wrapper
function initializeWithJavaWrapper() {
    console.log("🔄 Trying initialization with Java.perform wrapper...");

    try {
        Java.perform(() => {
            console.log("✅ Inside Java.perform - now attempting IL2CPP initialization...");
            initializeIL2CPP();
        });
    } catch (javaError) {
        console.log(`⚠️ Java.perform failed: ${javaError}`);
        console.log("🔄 Falling back to direct IL2CPP initialization...");
        initializeIL2CPP();
    }
}

setTimeout(() => {
    console.log("🚀 Starting IL2CPP initialization process...");

    // Try Java.perform wrapper first for Android IL2CPP games
    if (Java.available) {
        console.log("✅ Java runtime available - using Java.perform wrapper");
        initializeWithJavaWrapper();
    } else {
        console.log("⚠️ Java runtime not available - using direct IL2CPP initialization");
        initializeIL2CPP();
    }
}, 8000); // Increased delay to 8 seconds for complex Unity games

// Also provide a manual initialization function
this.reinitializeHook = function() {
    console.log("� Manual reinitialization requested...");
    isHooked = false;
    EntityControllerClass = null;
    assemblyImage = null;
    initializeIL2CPP();
};

console.log("💡 If initialization fails, you can retry manually with: reinitializeHook()");
